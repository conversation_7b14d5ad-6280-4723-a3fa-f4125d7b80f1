import { supabase, type Tables, type Inserts, type Updates } from '@/lib/supabase'
import type {
  User,
  Contractor,
  Project,
  Bid,
  Conversation,
  Message,
  Review,
  Payment,
  SearchFilters,
  SearchResult
} from '@/types'

// Enhanced error handling
export class DatabaseError extends Error {
  constructor(message: string, public code?: string, public details?: any) {
    super(message)
    this.name = 'DatabaseError'
  }
}

// Response wrapper for consistent API responses
export interface ServiceResponse<T> {
  data: T | null
  error: string | null
  success: boolean
}

// Helper function to create service responses
export function createResponse<T>(data: T | null, error: string | null = null): ServiceResponse<T> {
  return {
    data,
    error,
    success: !error
  }
}

// Base service class with common CRUD operations
abstract class BaseService<T extends { id: string }> {
  protected tableName: string

  constructor(tableName: string) {
    this.tableName = tableName
  }

  async findById(id: string): Promise<ServiceResponse<T>> {
    try {
      const { data, error } = await supabase
        .from(this.tableName)
        .select('*')
        .eq('id', id)
        .single()

      if (error) {
        console.error(`Error fetching ${this.tableName}:`, {
          message: error.message,
          code: error.code,
          details: error.details,
          hint: error.hint
        })
        return createResponse(null, error.message || `Failed to fetch ${this.tableName}`)
      }

      return createResponse(data as T)
    } catch (error) {
      console.error(`Unexpected error in findById:`, error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  async findMany(filters?: Record<string, any>, limit?: number): Promise<ServiceResponse<T[]>> {
    try {
      let query = supabase.from(this.tableName).select('*')

      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            query = query.eq(key, value)
          }
        })
      }

      if (limit) {
        query = query.limit(limit)
      }

      const { data, error } = await query

      if (error) {
        console.error(`Error fetching ${this.tableName}:`, error)

        // Check if it's a table not found error
        if (error.message?.includes('relation') || error.message?.includes('does not exist')) {
          return createResponse([], null) // Return empty array instead of error for missing table
        }

        return createResponse(null, error.message)
      }

      return createResponse(data as T[] || [])
    } catch (error) {
      console.error(`Unexpected error in findMany:`, error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  async create(data: Omit<T, 'id' | 'created_at' | 'updated_at'>): Promise<ServiceResponse<T>> {
    try {
      const { data: result, error } = await supabase
        .from(this.tableName)
        .insert(data)
        .select()
        .single()

      if (error) {
        console.error(`Error creating ${this.tableName}:`, error)
        return createResponse(null, error.message)
      }

      return createResponse(result as T)
    } catch (error) {
      console.error(`Unexpected error in create:`, error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  async update(id: string, updates: Partial<Omit<T, 'id' | 'created_at'>>): Promise<ServiceResponse<T>> {
    try {
      const { data: result, error } = await supabase
        .from(this.tableName)
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', id)
        .select()
        .single()

      if (error) {
        console.error(`Error updating ${this.tableName}:`, error)
        return createResponse(null, error.message)
      }

      return createResponse(result as T)
    } catch (error) {
      console.error(`Unexpected error in update:`, error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  async delete(id: string): Promise<ServiceResponse<void>> {
    try {
      const { error } = await supabase
        .from(this.tableName)
        .delete()
        .eq('id', id)

      if (error) {
        console.error(`Error deleting ${this.tableName}:`, error)
        return createResponse(null, error.message)
      }

      return createResponse(null)
    } catch (error) {
      console.error(`Unexpected error in delete:`, error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  async search(query: string, columns: string[] = ['*']): Promise<ServiceResponse<T[]>> {
    try {
      let supabaseQuery = supabase
        .from(this.tableName)
        .select(columns.join(', '))

      // Add text search if query is provided
      if (query.trim()) {
        // This is a basic implementation - you might want to use full-text search
        supabaseQuery = supabaseQuery.or(
          columns.map(col => `${col}.ilike.%${query}%`).join(',')
        )
      }

      const { data, error } = await supabaseQuery

      if (error) {
        console.error(`Error searching ${this.tableName}:`, error)
        return createResponse(null, error.message)
      }

      return createResponse(data as T[] || [])
    } catch (error) {
      console.error(`Unexpected error in search:`, error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }
}

// User service
export class UserService extends BaseService<Tables<'users'>> {
  constructor() {
    super('users')
  }

  async findByEmail(email: string): Promise<ServiceResponse<Tables<'users'>>> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('email', email)
        .single()

      if (error) {
        console.error('Error fetching user by email:', error)
        return createResponse(null, error.message)
      }

      return createResponse(data)
    } catch (error) {
      console.error('Unexpected error in findByEmail:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  async updatePreferences(userId: string, preferences: any): Promise<ServiceResponse<boolean>> {
    try {
      const { error } = await supabase
        .from('users')
        .update({ preferences })
        .eq('id', userId)

      if (error) {
        console.error('Error updating user preferences:', error)
        return createResponse(null, error.message)
      }

      return createResponse(true)
    } catch (error) {
      console.error('Unexpected error in updatePreferences:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  async updateProfile(userId: string, updates: Partial<Tables<'users'>>): Promise<ServiceResponse<Tables<'users'>>> {
    try {
      const { data, error } = await supabase
        .from('users')
        .update(updates)
        .eq('id', userId)
        .select()
        .single()

      if (error) {
        console.error('Error updating user profile:', error)
        return createResponse(null, error.message)
      }

      return createResponse(data)
    } catch (error) {
      console.error('Unexpected error in updateProfile:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }
}

// Contractor service
export class ContractorService extends BaseService<Tables<'contractors'>> {
  constructor() {
    super('contractors')
  }

  async findByUserId(userId: string): Promise<ServiceResponse<Tables<'contractors'>>> {
    try {
      const { data, error } = await supabase
        .from('contractors')
        .select('*')
        .eq('user_id', userId)
        .single()

      if (error) {
        console.error('Error fetching contractor by user ID:', error)
        return createResponse(null, error.message)
      }

      return createResponse(data)
    } catch (error) {
      console.error('Unexpected error in findByUserId:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  async findWithUserData(contractorId: string): Promise<ServiceResponse<any>> {
    try {
      const { data, error } = await supabase
        .from('contractors')
        .select(`
          *,
          users (
            id,
            name,
            email,
            avatar_url,
            phone,
            location,
            verified
          )
        `)
        .eq('id', contractorId)
        .single()

      if (error) {
        console.error('Error fetching contractor with user data:', error)
        return createResponse(null, error.message)
      }

      return createResponse(data)
    } catch (error) {
      console.error('Unexpected error in findWithUserData:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  async searchContractors(filters: SearchFilters): Promise<ServiceResponse<SearchResult<any>>> {
    try {
      let query = supabase
        .from('contractors')
        .select(`
          *,
          users (
            id,
            name,
            email,
            avatar_url,
            phone,
            location,
            verified
          )
        `, { count: 'exact' })
        .eq('status', 'active')

      // Apply filters
      if (filters.category && filters.category.length > 0) {
        query = query.overlaps('specialties', filters.category)
      }

      if (filters.rating) {
        query = query.gte('rating_average', filters.rating)
      }

      if (filters.verified !== undefined) {
        query = query.eq('users.verified', filters.verified)
      }

      if (filters.location) {
        query = query.ilike('users.location', `%${filters.location}%`)
      }

      if (filters.search) {
        query = query.or(`business_name.ilike.%${filters.search}%,users.name.ilike.%${filters.search}%`)
      }

      // Apply sorting
      if (filters.sortBy) {
        const ascending = filters.sortOrder === 'asc'
        switch (filters.sortBy) {
          case 'rating':
            query = query.order('rating_average', { ascending })
            break
          case 'reviews':
            query = query.order('rating_count', { ascending })
            break
          case 'recent':
            query = query.order('created_at', { ascending })
            break
          default:
            query = query.order('rating_average', { ascending: false })
        }
      } else {
        query = query.order('rating_average', { ascending: false })
      }

      // Apply pagination
      if (filters.page && filters.limit) {
        const from = (filters.page - 1) * filters.limit
        const to = from + filters.limit - 1
        query = query.range(from, to)
      }

      const { data, error, count } = await query

      if (error) {
        console.error('Error searching contractors:', error)

        // Handle empty error objects or missing message
        const errorMessage = error.message || error.toString() || 'Unknown database error'

        // Check if it's a table not found error
        if (errorMessage.includes('relation') || errorMessage.includes('does not exist') || Object.keys(error).length === 0) {
          return createResponse({ items: [], total: 0, page: 1, limit: 10 }, null) // Return empty result instead of error
        }

        return createResponse(null, errorMessage)
      }

      const result: SearchResult<any> = {
        items: data || [],
        total: count || 0,
        filters
      }

      return createResponse(result)
    } catch (error) {
      console.error('Unexpected error in searchContractors:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  async updateRating(contractorId: string, newRating: number): Promise<ServiceResponse<boolean>> {
    try {
      // This would typically be called after a new review is added
      const { data: contractor } = await supabase
        .from('contractors')
        .select('rating_average, rating_count')
        .eq('id', contractorId)
        .single()

      if (!contractor) {
        return createResponse(null, 'Contractor not found')
      }

      const totalRating = contractor.rating_average * contractor.rating_count + newRating
      const newCount = contractor.rating_count + 1
      const newAverage = totalRating / newCount

      const { error } = await supabase
        .from('contractors')
        .update({
          rating_average: newAverage,
          rating_count: newCount
        })
        .eq('id', contractorId)

      if (error) {
        console.error('Error updating contractor rating:', error)
        return createResponse(null, error.message)
      }

      return createResponse(true)
    } catch (error) {
      console.error('Unexpected error in updateRating:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }
}

// Project service
export class ProjectService extends BaseService<Tables<'projects'>> {
  constructor() {
    super('projects')
  }

  async findByCustomerId(customerId: string): Promise<ServiceResponse<Tables<'projects'>[]>> {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select(`
          *,
          users!customer_id (
            id,
            name,
            email,
            avatar_url,
            location
          ),
          contractors (
            id,
            business_name,
            rating_average,
            users!user_id (
              name,
              avatar_url
            )
          )
        `)
        .eq('customer_id', customerId)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching projects by customer:', error)

        // Handle empty error objects or missing message
        const errorMessage = error.message || error.toString() || 'Unknown database error'

        // Check if it's a table not found error
        if (errorMessage.includes('relation') || errorMessage.includes('does not exist') || Object.keys(error).length === 0) {
          return createResponse([], null) // Return empty array instead of error for missing table or empty error
        }

        return createResponse(null, errorMessage)
      }

      return createResponse(data || [])
    } catch (error) {
      console.error('Unexpected error in findByCustomerId:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  async findByContractorId(contractorId: string): Promise<ServiceResponse<Tables<'projects'>[]>> {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select(`
          *,
          users!customer_id (
            id,
            name,
            email,
            avatar_url,
            location
          )
        `)
        .eq('contractor_id', contractorId)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching projects by contractor:', error)
        return createResponse(null, error.message)
      }

      return createResponse(data || [])
    } catch (error) {
      console.error('Unexpected error in findByContractorId:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  async findActiveProjects(category?: string): Promise<Tables<'projects'>[]> {
    let query = supabase
      .from('projects')
      .select('*')
      .eq('status', 'active')
      .order('created_at', { ascending: false })

    if (category) {
      query = query.eq('category', category)
    }

    const { data, error } = await query

    if (error) {
      console.error('Error fetching active projects:', error)
      return []
    }

    return data || []
  }

  async updateStatus(projectId: string, status: string): Promise<boolean> {
    const updateData: any = { status }
    
    if (status === 'completed') {
      updateData.completed_at = new Date().toISOString()
    }

    const { error } = await supabase
      .from('projects')
      .update(updateData)
      .eq('id', projectId)

    if (error) {
      console.error('Error updating project status:', error)
      return false
    }

    return true
  }

  async selectBid(projectId: string, bidId: string, contractorId: string): Promise<boolean> {
    const { error } = await supabase
      .from('projects')
      .update({
        selected_bid_id: bidId,
        contractor_id: contractorId,
        status: 'in-progress'
      })
      .eq('id', projectId)

    if (error) {
      console.error('Error selecting bid:', error)
      return false
    }

    return true
  }

  async findAvailableProjects(contractorId?: string): Promise<ServiceResponse<Tables<'projects'>[]>> {
    try {
      let query = supabase
        .from('projects')
        .select(`
          *,
          users!customer_id (
            id,
            name,
            email,
            avatar_url,
            location
          )
        `)
        .eq('status', 'active')
        .is('contractor_id', null)
        .order('created_at', { ascending: false })

      // If contractor ID provided, exclude projects they've already bid on
      if (contractorId) {
        const { data: existingBids } = await supabase
          .from('bids')
          .select('project_id')
          .eq('contractor_id', contractorId)

        if (existingBids && existingBids.length > 0) {
          const bidProjectIds = existingBids.map(bid => bid.project_id)
          query = query.not('id', 'in', `(${bidProjectIds.join(',')})`)
        }
      }

      const { data, error } = await query

      if (error) {
        console.error('Error fetching available projects:', error)
        return createResponse(null, error.message)
      }

      return createResponse(data || [])
    } catch (error) {
      console.error('Unexpected error in findAvailableProjects:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  async searchProjects(filters: {
    query?: string
    category?: string[]
    location?: string
    budgetMin?: number
    budgetMax?: number
    status?: string[]
  }): Promise<ServiceResponse<Tables<'projects'>[]>> {
    try {
      let query = supabase
        .from('projects')
        .select(`
          *,
          users!customer_id (
            id,
            name,
            email,
            avatar_url,
            location
          ),
          contractors (
            id,
            business_name,
            rating_average,
            rating_count
          )
        `)

      // Apply filters
      if (filters.query) {
        query = query.or(`title.ilike.%${filters.query}%,description.ilike.%${filters.query}%`)
      }

      if (filters.category && filters.category.length > 0) {
        query = query.in('category', filters.category)
      }

      if (filters.status && filters.status.length > 0) {
        query = query.in('status', filters.status)
      }

      if (filters.location) {
        query = query.ilike('location->>address', `%${filters.location}%`)
      }

      query = query.order('created_at', { ascending: false })

      const { data, error } = await query

      if (error) {
        console.error('Error searching projects:', error)
        return createResponse(null, error.message)
      }

      return createResponse(data || [])
    } catch (error) {
      console.error('Unexpected error in searchProjects:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }
}

// Bid service
export class BidService extends BaseService<Tables<'bids'>> {
  constructor() {
    super('bids')
  }

  async findByProjectId(projectId: string): Promise<ServiceResponse<Tables<'bids'>[]>> {
    try {
      const { data, error } = await supabase
        .from('bids')
        .select('*, contractors(*, users(*))')
        .eq('project_id', projectId)
        .order('amount', { ascending: true })

      if (error) {
        console.error('Error fetching bids by project:', error)
        return createResponse(null, error.message)
      }

      return createResponse(data || [])
    } catch (error) {
      console.error('Unexpected error in findByProjectId:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  async findByContractorId(contractorId: string): Promise<ServiceResponse<Tables<'bids'>[]>> {
    try {
      const { data, error } = await supabase
        .from('bids')
        .select('*, projects(*)')
        .eq('contractor_id', contractorId)
        .order('submitted_at', { ascending: false })

      if (error) {
        console.error('Error fetching bids by contractor:', error)
        return createResponse(null, error.message)
      }

      return createResponse(data || [])
    } catch (error) {
      console.error('Unexpected error in findByContractorId:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  async updateStatus(bidId: string, status: string): Promise<boolean> {
    const { error } = await supabase
      .from('bids')
      .update({ status })
      .eq('id', bidId)

    if (error) {
      console.error('Error updating bid status:', error)
      return false
    }

    return true
  }
}

// Message service
export class MessageService extends BaseService<Tables<'messages'>> {
  constructor() {
    super('messages')
  }

  async findByConversationId(conversationId: string): Promise<ServiceResponse<Tables<'messages'>[]>> {
    try {
      const { data, error } = await supabase
        .from('messages')
        .select(`
          *,
          users!sender_id (
            id,
            name,
            avatar_url,
            role
          )
        `)
        .eq('conversation_id', conversationId)
        .order('sent_at', { ascending: true })

      if (error) {
        console.error('Error fetching messages:', error)
        return createResponse(null, error.message)
      }

      return createResponse(data || [])
    } catch (error) {
      console.error('Unexpected error in findByConversationId:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  async markAsRead(messageId: string): Promise<ServiceResponse<boolean>> {
    try {
      const { error } = await supabase
        .from('messages')
        .update({
          status: 'read',
          read_at: new Date().toISOString()
        })
        .eq('id', messageId)

      if (error) {
        console.error('Error marking message as read:', error)
        return createResponse(null, error.message)
      }

      return createResponse(true)
    } catch (error) {
      console.error('Unexpected error in markAsRead:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  async findUnreadCount(userId: string): Promise<ServiceResponse<number>> {
    try {
      const { count, error } = await supabase
        .from('messages')
        .select('id', { count: 'exact' })
        .neq('sender_id', userId)
        .eq('status', 'sent')

      if (error) {
        console.error('Error fetching unread count:', error)
        return createResponse(null, error.message)
      }

      return createResponse(count || 0)
    } catch (error) {
      console.error('Unexpected error in findUnreadCount:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }
}

// Conversation service
export class ConversationService extends BaseService<Tables<'conversations'>> {
  constructor() {
    super('conversations')
  }

  async findByUserId(userId: string): Promise<ServiceResponse<Tables<'conversations'>[]>> {
    try {
      const { data, error } = await supabase
        .from('conversations')
        .select(`
          *,
          projects (
            id,
            title,
            category,
            status
          ),
          messages (
            id,
            content,
            sent_at,
            status,
            users!sender_id (
              id,
              name,
              avatar_url
            )
          )
        `)
        .contains('participants', [userId])
        .order('updated_at', { ascending: false })

      if (error) {
        console.error('Error fetching conversations:', error)
        return createResponse(null, error.message)
      }

      // Process conversations to add latest message and unread count
      const processedData = (data || []).map(conversation => ({
        ...conversation,
        latestMessage: conversation.messages?.[conversation.messages.length - 1] || null,
        unreadCount: conversation.messages?.filter((msg: any) =>
          msg.sender_id !== userId && msg.status === 'sent'
        ).length || 0
      }))

      return createResponse(processedData)
    } catch (error) {
      console.error('Unexpected error in findByUserId:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  async findByProjectId(projectId: string): Promise<ServiceResponse<Tables<'conversations'> | null>> {
    try {
      const { data, error } = await supabase
        .from('conversations')
        .select('*')
        .eq('project_id', projectId)
        .single()

      if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
        console.error('Error fetching conversation by project:', error)
        return createResponse(null, error.message)
      }

      return createResponse(data || null)
    } catch (error) {
      console.error('Unexpected error in findByProjectId:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  async findByProjectId(projectId: string): Promise<Tables<'conversations'> | null> {
    const { data, error } = await supabase
      .from('conversations')
      .select('*')
      .eq('project_id', projectId)
      .single()

    if (error) {
      console.error('Error fetching conversation by project:', error)
      return null
    }

    return data
  }

  async updateUnreadCount(conversationId: string, count: number): Promise<boolean> {
    const { error } = await supabase
      .from('conversations')
      .update({ unread_count: count })
      .eq('id', conversationId)

    if (error) {
      console.error('Error updating unread count:', error)
      return false
    }

    return true
  }
}

// Review service
export class ReviewService extends BaseService<Tables<'reviews'>> {
  constructor() {
    super('reviews')
  }

  async findByRevieweeId(revieweeId: string): Promise<ServiceResponse<Tables<'reviews'>[]>> {
    try {
      const { data, error } = await supabase
        .from('reviews')
        .select('*, projects(*), users!reviewer_id(*)')
        .eq('reviewee_id', revieweeId)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching reviews by reviewee:', error)
        return createResponse(null, error.message)
      }

      return createResponse(data || [])
    } catch (error) {
      console.error('Unexpected error in findByRevieweeId:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  async findByContractorId(contractorId: string): Promise<ServiceResponse<Tables<'reviews'>[]>> {
    try {
      const { data, error } = await supabase
        .from('reviews')
        .select('*, projects(*), users!reviewer_id(*)')
        .eq('reviewee_id', contractorId)
        .order('created_at', { ascending: false })

      if (error) {
        // Handle empty error objects or missing message first
        const errorMessage = error.message || error.toString() || 'Unknown database error'
        const isEmptyError = Object.keys(error).length === 0

        // Log the error with better formatting
        if (isEmptyError) {
          console.error('Error fetching reviews by contractor: Empty error object')
        } else {
          console.error('Error fetching reviews by contractor:', {
            message: error.message,
            code: error.code,
            details: error.details,
            hint: error.hint
          })
        }

        // Check if it's a table not found error or empty error
        if (errorMessage.includes('relation') || errorMessage.includes('does not exist') || isEmptyError) {
          return createResponse([], null) // Return empty array instead of error for missing table or empty error
        }

        return createResponse(null, errorMessage)
      }

      return createResponse(data || [])
    } catch (error) {
      console.error('Unexpected error in findByContractorId:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  async findByCustomerId(customerId: string): Promise<ServiceResponse<Tables<'reviews'>[]>> {
    try {
      const { data, error } = await supabase
        .from('reviews')
        .select('*, projects(*), users!reviewee_id(*)')
        .eq('reviewer_id', customerId)
        .order('created_at', { ascending: false })

      if (error) {
        // Handle empty error objects or missing message first
        const errorMessage = error.message || error.toString() || 'Unknown database error'
        const isEmptyError = Object.keys(error).length === 0

        // Log the error with better formatting
        if (isEmptyError) {
          console.error('Error fetching reviews by customer: Empty error object')
        } else {
          console.error('Error fetching reviews by customer:', {
            message: error.message,
            code: error.code,
            details: error.details,
            hint: error.hint
          })
        }

        // Check if it's a table not found error or empty error
        if (errorMessage.includes('relation') || errorMessage.includes('does not exist') || isEmptyError) {
          return createResponse([], null) // Return empty array instead of error for missing table or empty error
        }

        return createResponse(null, errorMessage)
      }

      return createResponse(data || [])
    } catch (error) {
      console.error('Unexpected error in findByCustomerId:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  async findByProjectId(projectId: string): Promise<ServiceResponse<Tables<'reviews'>[]>> {
    try {
      // Check authentication state
      const { data: { session } } = await supabase.auth.getSession()
      console.log('ReviewService.findByProjectId - Auth session:', session ? 'authenticated' : 'not authenticated')
      console.log('ReviewService.findByProjectId - Project ID:', projectId)

      const { data, error } = await supabase
        .from('reviews')
        .select('*')
        .eq('project_id', projectId)
        .order('created_at', { ascending: false })

      console.log('ReviewService.findByProjectId - Query result:', { data: data?.length || 0, error })

      if (error) {
        // Handle empty error objects or missing message first
        const errorMessage = error.message || error.toString() || 'Unknown database error'
        const isEmptyError = Object.keys(error).length === 0

        // Log the error with better formatting
        if (isEmptyError) {
          console.error('Error fetching reviews by project: Empty error object')
        } else {
          console.error('Error fetching reviews by project:', {
            message: error.message,
            code: error.code,
            details: error.details,
            hint: error.hint
          })
        }

        // Check if it's a table not found error or empty error
        if (errorMessage.includes('relation') || errorMessage.includes('does not exist') || isEmptyError) {
          return createResponse([], null) // Return empty array instead of error for missing table or empty error
        }

        return createResponse(null, errorMessage)
      }

      return createResponse(data || [])
    } catch (error) {
      console.error('Unexpected error in findByProjectId:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }
}

// Payment service
export class PaymentService extends BaseService<Tables<'payments'>> {
  constructor() {
    super('payments')
  }

  async findByUserId(userId: string): Promise<Tables<'payments'>[]> {
    const { data, error } = await supabase
      .from('payments')
      .select('*, projects(*), bids(*)')
      .or(`payer_id.eq.${userId},payee_id.eq.${userId}`)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching payments by user:', error)
      return []
    }

    return data || []
  }

  async updateStatus(paymentId: string, status: string): Promise<boolean> {
    const updateData: any = { status }

    if (status === 'completed') {
      updateData.processed_at = new Date().toISOString()
    } else if (status === 'refunded') {
      updateData.refunded_at = new Date().toISOString()
    }

    const { error } = await supabase
      .from('payments')
      .update(updateData)
      .eq('id', paymentId)

    if (error) {
      console.error('Error updating payment status:', error)
      return false
    }

    return true
  }
}

// Export service instances
export const userService = new UserService()
export const contractorService = new ContractorService()
export const projectService = new ProjectService()
export const bidService = new BidService()
export const messageService = new MessageService()
export const conversationService = new ConversationService()
export const reviewService = new ReviewService()
export const paymentService = new PaymentService()

// Export types for convenience
export type { ServiceResponse, DatabaseError }
