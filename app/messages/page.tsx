"use client"

import React, { useState, useEffect } from 'react'
import { UnifiedNavigation } from '@/components/unified-navigation'
import { ConversationList } from '@/components/messaging/conversation-list'
import { MessageThread } from '@/components/messaging/message-thread'
import { MessagingErrorBoundary, ConnectionStatus } from '@/components/messaging/messaging-error-boundary'
import { useUser } from '@/contexts/user-context'
import { useMessaging } from '@/hooks/use-messaging'
import { useRealtimeMessaging } from '@/hooks/use-realtime-messaging'
import { useProjects } from '@/hooks/use-projects'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { MessageCircle, Plus, Loader2, RefreshCw } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

export default function MessagesPage() {
  const { user } = useUser()
  const { toast } = useToast()
  const [selectedConversationId, setSelectedConversationId] = useState<string | null>(null)
  const [showNewConversationDialog, setShowNewConversationDialog] = useState(false)
  const [selectedProjectId, setSelectedProjectId] = useState<string>('')

  const {
    conversations,
    messages,
    currentConversation,
    loading,
    error,
    sendMessage,
    createConversation,
    selectConversation,
    markAsRead,
    refetch
  } = useMessaging({
    conversationId: selectedConversationId || undefined,
    autoFetch: true
  })

  // Get user's projects for conversation creation
  const { projects, loading: projectsLoading } = useProjects({
    autoFetch: true,
    includeCompleted: false
  })

  // Get connection status from real-time messaging
  const { isConnected } = useRealtimeMessaging({
    conversationId: selectedConversationId || undefined
  })

  // Handle URL parameters for direct conversation access
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    const conversationParam = urlParams.get('conversation')
    if (conversationParam && conversationParam !== selectedConversationId) {
      setSelectedConversationId(conversationParam)
      selectConversation(conversationParam)
    }
  }, [])

  // Handle conversation selection
  const handleSelectConversation = (conversationId: string) => {
    setSelectedConversationId(conversationId)
    selectConversation(conversationId)
  }

  // Handle creating new conversation
  const handleCreateConversation = async () => {
    if (!selectedProjectId || !user) {
      toast({
        title: "Error",
        description: "Please select a project",
        variant: "destructive"
      })
      return
    }

    try {
      // Find the selected project to get contractor information
      const selectedProject = projects.find(p => p.id === selectedProjectId)
      if (!selectedProject) {
        toast({
          title: "Error",
          description: "Selected project not found",
          variant: "destructive"
        })
        return
      }

      // Determine participants based on user role and project status
      let participants = [user.id]

      if (user.role === 'customer' && selectedProject.contractor_id) {
        // Customer starting conversation with assigned contractor
        participants.push(selectedProject.contractor_id)
      } else if (user.role === 'pro' && selectedProject.customer_id) {
        // Contractor starting conversation with customer
        participants.push(selectedProject.customer_id)
      }

      if (participants.length < 2) {
        toast({
          title: "Error",
          description: "Cannot create conversation: missing participant information",
          variant: "destructive"
        })
        return
      }

      const conversationId = await createConversation(selectedProjectId, participants)
      if (conversationId) {
        setSelectedConversationId(conversationId)
        setShowNewConversationDialog(false)
        setSelectedProjectId('')
        toast({
          title: "Success",
          description: "Conversation created successfully"
        })
      }
    } catch (error) {
      console.error('Error creating conversation:', error)
      toast({
        title: "Error",
        description: "Failed to create conversation",
        variant: "destructive"
      })
    }
  }

  // Handle sending messages
  const handleSendMessage = async (content: string, attachments?: File[]) => {
    if (!currentConversation) return false

    try {
      const success = await sendMessage(content, attachments)
      if (success) {
        // Mark message as read immediately for current user
        await markAsRead(currentConversation.id)
      }
      return success
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send message",
        variant: "destructive"
      })
      return false
    }
  }

  // Handle marking messages as read
  const handleMarkAsRead = async (_messageId: string) => {
    if (currentConversation) {
      await markAsRead(currentConversation.id)
    }
  }

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen bg-slate-50">
        <UnifiedNavigation />
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <MessageCircle className="h-12 w-12 mx-auto mb-4 text-red-300" />
            <p className="text-red-500 mb-4">{error}</p>
            <Button onClick={refetch} variant="outline">
              Try Again
            </Button>
          </div>
        </div>
      </div>
    )
  }

  // Show login required state
  if (!user) {
    return (
      <div className="min-h-screen bg-slate-50">
        <UnifiedNavigation />
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <MessageCircle className="h-12 w-12 mx-auto mb-4 text-slate-300" />
            <h3 className="text-lg font-medium text-slate-900 mb-2">
              Authentication Required
            </h3>
            <p className="text-slate-500 mb-4">
              Please log in to access your messages
            </p>
            <Button onClick={() => window.location.href = '/auth/login'}>
              Sign In
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-50">
      <UnifiedNavigation />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 md:py-8">
        <MessagingErrorBoundary isConnected={isConnected}>
          <div className="bg-white rounded-lg shadow-sm border h-[calc(100vh-8rem)] md:h-[calc(100vh-12rem)]">
            <div className="flex h-full">
              {/* Conversation List */}
              <div className={`${
                selectedConversationId ? 'hidden md:flex' : 'flex'
              } w-full md:w-80 lg:w-96 border-r flex-col`}>
                <div className="p-4 border-b">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <h3 className="font-semibold text-slate-900">Messages</h3>
                      <ConnectionStatus isConnected={isConnected} />
                    </div>
                    <Dialog open={showNewConversationDialog} onOpenChange={setShowNewConversationDialog}>
                      <DialogTrigger asChild>
                        <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                          <Plus className="h-4 w-4 mr-1" />
                          New
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Start New Conversation</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div>
                            <label className="text-sm font-medium text-slate-700 mb-2 block">
                              Select Project
                            </label>
                            <Select value={selectedProjectId} onValueChange={setSelectedProjectId}>
                              <SelectTrigger>
                                <SelectValue placeholder={projectsLoading ? "Loading projects..." : "Choose a project..."} />
                              </SelectTrigger>
                              <SelectContent>
                                {projectsLoading ? (
                                  <SelectItem value="" disabled>
                                    <div className="flex items-center space-x-2">
                                      <Loader2 className="h-4 w-4 animate-spin" />
                                      <span>Loading projects...</span>
                                    </div>
                                  </SelectItem>
                                ) : projects.length === 0 ? (
                                  <SelectItem value="" disabled>
                                    No projects available
                                  </SelectItem>
                                ) : (
                                  projects.map((project) => (
                                    <SelectItem key={project.id} value={project.id}>
                                      <div className="flex flex-col">
                                        <span className="font-medium">{project.title}</span>
                                        <span className="text-xs text-slate-500 capitalize">
                                          {project.category} • {project.status}
                                        </span>
                                      </div>
                                    </SelectItem>
                                  ))
                                )}
                              </SelectContent>
                            </Select>
                            {projects.length === 0 && !projectsLoading && (
                              <p className="text-xs text-slate-500 mt-1">
                                You need to have active projects to start conversations
                              </p>
                            )}
                          </div>
                          <div className="flex justify-end space-x-2">
                            <Button variant="outline" onClick={() => setShowNewConversationDialog(false)}>
                              Cancel
                            </Button>
                            <Button
                              onClick={handleCreateConversation}
                              disabled={!selectedProjectId || projectsLoading}
                            >
                              {projectsLoading ? (
                                <>
                                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                  Loading...
                                </>
                              ) : (
                                'Create Conversation'
                              )}
                            </Button>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>

                <div className="flex-1 overflow-hidden">
                  {loading ? (
                    <div className="flex items-center justify-center h-32">
                      <Loader2 className="h-6 w-6 animate-spin text-slate-400" />
                    </div>
                  ) : (
                    <ConversationList
                      conversations={conversations}
                      selectedConversationId={selectedConversationId || undefined}
                      onSelectConversation={handleSelectConversation}
                      loading={loading}
                      currentUserId={user.id}
                    />
                  )}
                </div>
              </div>

            {/* Message Thread */}
            <div className={`${
              selectedConversationId ? 'flex' : 'hidden md:flex'
            } flex-1 flex-col min-w-0`}>
              {selectedConversationId && currentConversation ? (
                <div className="flex flex-col h-full">
                  {/* Mobile Back Button */}
                  <div className="md:hidden flex items-center p-4 border-b bg-slate-50">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setSelectedConversationId(null)}
                      className="mr-3"
                    >
                      ← Back
                    </Button>
                    <h2 className="font-medium text-slate-900 truncate">
                      {currentConversation.projects?.title || 'Conversation'}
                    </h2>
                  </div>

                  <MessageThread
                    messages={messages}
                    currentUserId={user.id}
                    onSendMessage={handleSendMessage}
                    onMarkAsRead={handleMarkAsRead}
                    loading={loading}
                    conversationTitle={currentConversation.projects?.title || 'Conversation'}
                  />
                </div>
              ) : (
                <div className="flex-1 flex items-center justify-center p-8">
                  <div className="text-center max-w-sm">
                    <MessageCircle className="h-16 w-16 mx-auto mb-4 text-slate-300" />
                    <h3 className="text-lg font-medium text-slate-900 mb-2">
                      Select a conversation
                    </h3>
                    <p className="text-slate-500 mb-4">
                      Choose a conversation from the list to start messaging
                    </p>
                    {conversations.length === 0 && (
                      <Button
                        onClick={() => setShowNewConversationDialog(true)}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Start Your First Conversation
                      </Button>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
        </MessagingErrorBoundary>
      </div>
    </div>
  )
}
