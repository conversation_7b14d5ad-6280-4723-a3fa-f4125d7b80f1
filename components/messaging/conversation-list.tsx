"use client"

import { useState } from 'react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { MessageCircle, Search, Plus, Clock } from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'

interface Conversation {
  id: string
  project_id: string
  participants: string[]
  created_at: string
  updated_at: string
  projects?: {
    id: string
    title: string
    category: string
    status: string
  }
  participants_data?: Array<{
    id: string
    name: string
    avatar_url?: string
    role: string
  }>
  latestMessage?: {
    id: string
    content: string
    sent_at: string
    status: string
    type: string
    users: {
      id: string
      name: string
      avatar_url?: string
    }
  } | null
  unreadCount: number
}

interface ConversationListProps {
  conversations: Conversation[]
  selectedConversationId?: string
  onSelectConversation: (conversationId: string) => void
  onNewConversation?: () => void
  loading?: boolean
  currentUserId: string
}

export function ConversationList({
  conversations,
  selectedConversationId,
  onSelectConversation,
  onNewConversation,
  loading = false,
  currentUserId
}: ConversationListProps) {
  const [searchQuery, setSearchQuery] = useState('')

  const filteredConversations = conversations.filter(conversation =>
    conversation.projects?.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    conversation.latestMessage?.content.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const getOtherParticipant = (conversation: Conversation) => {
    if (conversation.participants_data && conversation.participants_data.length > 0) {
      return conversation.participants_data[0]
    }
    return {
      id: conversation.participants.find(id => id !== currentUserId) || 'unknown',
      name: 'Unknown User',
      avatar_url: null,
      role: 'customer'
    }
  }

  const formatMessageTime = (timestamp: string) => {
    return formatDistanceToNow(new Date(timestamp), { addSuffix: true })
  }

  const getMessagePreview = (message: Conversation['latestMessage']) => {
    if (!message) return 'No messages yet'

    if (message.type === 'image') return '📷 Image'
    if (message.type === 'file') return '📎 File'
    if (message.type === 'system') return message.content

    return message.content.length > 50
      ? message.content.substring(0, 50) + '...'
      : message.content
  }

  const truncateMessage = (content: string, maxLength: number = 50) => {
    return content.length > maxLength ? `${content.substring(0, maxLength)}...` : content
  }

  if (loading) {
    return (
      <div className="h-full flex flex-col">
        <div className="p-4 border-b">
          <div className="h-10 bg-slate-200 rounded animate-pulse mb-4"></div>
          <div className="h-8 bg-slate-200 rounded animate-pulse"></div>
        </div>
        <div className="flex-1 p-4 space-y-4">
          {Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className="flex items-center space-x-3 animate-pulse">
              <div className="w-12 h-12 bg-slate-200 rounded-full"></div>
              <div className="flex-1">
                <div className="h-4 bg-slate-200 rounded mb-2"></div>
                <div className="h-3 bg-slate-200 rounded w-3/4"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col bg-white border-r">
      {/* Header */}
      <div className="p-4 border-b bg-slate-50">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-slate-900 flex items-center">
            <MessageCircle className="h-5 w-5 mr-2 text-blue-600" />
            Messages
          </h2>
          {onNewConversation && (
            <Button
              size="sm"
              onClick={onNewConversation}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Plus className="h-4 w-4" />
            </Button>
          )}
        </div>
        
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
          <Input
            placeholder="Search conversations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Conversation List */}
      <ScrollArea className="flex-1">
        <div className="p-2">
          {filteredConversations.length === 0 ? (
            <div className="text-center py-8 text-slate-500">
              <MessageCircle className="h-12 w-12 mx-auto mb-4 text-slate-300" />
              <p className="text-sm">
                {searchQuery ? 'No conversations found' : 'No conversations yet'}
              </p>
              {!searchQuery && onNewConversation && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onNewConversation}
                  className="mt-2"
                >
                  Start a conversation
                </Button>
              )}
            </div>
          ) : (
            filteredConversations.map((conversation) => (
              <div
                key={conversation.id}
                onClick={() => onSelectConversation(conversation.id)}
                className={`p-3 rounded-lg cursor-pointer transition-colors mb-2 ${
                  selectedConversationId === conversation.id
                    ? 'bg-blue-50 border border-blue-200'
                    : 'hover:bg-slate-50'
                }`}
              >
                <div className="flex items-start space-x-3">
                  {/* Avatar */}
                  <Avatar className="h-10 w-10 flex-shrink-0">
                    <AvatarImage src="/placeholder.svg" />
                    <AvatarFallback className="bg-blue-100 text-blue-600">
                      {conversation.projects?.title.charAt(0) || 'P'}
                    </AvatarFallback>
                  </Avatar>

                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <h3 className="text-sm font-medium text-slate-900 truncate">
                        {conversation.projects?.title || 'Project Discussion'}
                      </h3>
                      <div className="flex items-center space-x-2">
                        {conversation.unreadCount > 0 && (
                          <Badge variant="destructive" className="text-xs px-1.5 py-0.5">
                            {conversation.unreadCount}
                          </Badge>
                        )}
                        {conversation.latestMessage && (
                          <span className="text-xs text-slate-500 flex items-center">
                            <Clock className="h-3 w-3 mr-1" />
                            {formatMessageTime(conversation.latestMessage.sent_at)}
                          </span>
                        )}
                      </div>
                    </div>
                    
                    <p className="text-xs text-slate-600 mb-1">
                      {conversation.projects?.category} • {conversation.projects?.status}
                    </p>
                    
                    {conversation.latestMessage && (
                      <p className="text-sm text-slate-600 truncate">
                        <span className="font-medium">
                          {conversation.latestMessage.users.name}:
                        </span>{' '}
                        {truncateMessage(conversation.latestMessage.content)}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </ScrollArea>
    </div>
  )
}
