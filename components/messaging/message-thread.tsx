"use client"

import { useState, useRef, useEffect } from 'react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import { MessageAttachmentUpload } from './message-attachment-upload'
import { Send, Paperclip, Image, File, Download, Check, CheckCheck, X } from 'lucide-react'
import { formatDistanceToNow, format, isToday, isYesterday } from 'date-fns'

interface Message {
  id: string
  conversation_id: string
  sender_id: string
  content: string
  type: 'text' | 'image' | 'file' | 'system'
  attachments?: Array<{
    id?: string
    name: string
    url: string
    type: string
    size: number
    thumbnail?: string
  }>
  sent_at: string
  status: 'sending' | 'sent' | 'delivered' | 'read'
  users: {
    id: string
    name: string
    avatar_url?: string
    role: string
  }
}

interface MessageThreadProps {
  messages: Message[]
  currentUserId: string
  onSendMessage: (content: string, attachments?: File[]) => Promise<boolean>
  onMarkAsRead: (messageId: string) => void
  loading?: boolean
  conversationTitle?: string
}

export function MessageThread({
  messages,
  currentUserId,
  onSendMessage,
  onMarkAsRead,
  loading = false,
  conversationTitle
}: MessageThreadProps) {
  const [newMessage, setNewMessage] = useState('')
  const [attachments, setAttachments] = useState<File[]>([])
  const [sending, setSending] = useState(false)
  const [showAttachments, setShowAttachments] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // Mark messages as read when they come into view
  useEffect(() => {
    const unreadMessages = messages.filter(
      msg => msg.sender_id !== currentUserId && msg.status !== 'read'
    )
    unreadMessages.forEach(msg => onMarkAsRead(msg.id))
  }, [messages, currentUserId, onMarkAsRead])

  const handleSendMessage = async () => {
    if (!newMessage.trim() && attachments.length === 0) return

    setSending(true)
    try {
      const success = await onSendMessage(newMessage, attachments)
      if (success) {
        setNewMessage('')
        setAttachments([])
      }
    } finally {
      setSending(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const handleFilesSelected = (files: File[]) => {
    setAttachments(prev => [...prev, ...files])
    setShowAttachments(true)
  }

  const handleFileRemoved = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index))
    if (attachments.length <= 1) {
      setShowAttachments(false)
    }
  }

  const toggleAttachments = () => {
    setShowAttachments(!showAttachments)
  }

  const formatMessageTime = (timestamp: string) => {
    const date = new Date(timestamp)
    if (isToday(date)) {
      return format(date, 'HH:mm')
    } else if (isYesterday(date)) {
      return `Yesterday ${format(date, 'HH:mm')}`
    } else {
      return format(date, 'MMM d, HH:mm')
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
        return <Check className="h-3 w-3 text-slate-400" />
      case 'delivered':
        return <CheckCheck className="h-3 w-3 text-slate-400" />
      case 'read':
        return <CheckCheck className="h-3 w-3 text-blue-500" />
      default:
        return null
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const renderAttachment = (attachment: Message['attachments'][0], index: number) => {
    const isImage = attachment.type.startsWith('image/')

    return (
      <div key={index} className="mt-2 p-2 bg-slate-50 rounded-lg border max-w-xs">
        {isImage ? (
          <div className="space-y-2">
            <img
              src={attachment.url}
              alt={attachment.name}
              className="max-w-full h-auto rounded cursor-pointer hover:opacity-90 transition-opacity"
              onClick={() => window.open(attachment.url, '_blank')}
            />
            <div className="flex items-center justify-between text-xs text-slate-600">
              <span className="truncate">{attachment.name}</span>
              <span>{formatFileSize(attachment.size)}</span>
            </div>
          </div>
        ) : (
          <div className="flex items-center space-x-2">
            <File className="h-8 w-8 text-slate-400 flex-shrink-0" />
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-slate-900 truncate">
                {attachment.name}
              </p>
              <p className="text-xs text-slate-500">
                {formatFileSize(attachment.size)}
              </p>
            </div>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => window.open(attachment.url, '_blank')}
              className="flex-shrink-0"
            >
              <Download className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>
    )
  }

  if (loading) {
    return (
      <div className="h-full flex flex-col">
        <div className="p-4 border-b">
          <div className="h-6 bg-slate-200 rounded animate-pulse"></div>
        </div>
        <div className="flex-1 p-4 space-y-4">
          {Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className="flex items-start space-x-3 animate-pulse">
              <div className="w-8 h-8 bg-slate-200 rounded-full"></div>
              <div className="flex-1">
                <div className="h-4 bg-slate-200 rounded mb-2 w-3/4"></div>
                <div className="h-3 bg-slate-200 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      {conversationTitle && (
        <div className="p-3 md:p-4 border-b bg-slate-50">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 min-w-0">
              <h3 className="font-semibold text-slate-900 truncate">{conversationTitle}</h3>
              <Badge variant="outline" className="text-xs flex-shrink-0">
                {messages.length}
              </Badge>
            </div>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setShowAttachments(!showAttachments)}
              className={`${showAttachments ? 'bg-slate-200' : ''} h-8 w-8 p-0 md:h-auto md:w-auto md:px-3`}
            >
              <Paperclip className="h-4 w-4" />
              <span className="hidden md:inline ml-2">Attach</span>
            </Button>
          </div>
        </div>
      )}

      {/* Messages */}
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-4">
          {messages.length === 0 ? (
            <div className="text-center py-8 text-slate-500">
              <p>No messages yet. Start the conversation!</p>
            </div>
          ) : (
            messages.map((message) => {
              const isOwn = message.sender_id === currentUserId
              return (
                <div
                  key={message.id}
                  className={`flex ${isOwn ? 'justify-end' : 'justify-start'}`}
                >
                  <div className={`flex items-start space-x-2 max-w-[85%] sm:max-w-xs lg:max-w-md ${isOwn ? 'flex-row-reverse space-x-reverse' : ''}`}>
                    {!isOwn && (
                      <Avatar className="h-8 w-8 flex-shrink-0">
                        <AvatarImage src={message.users.avatar_url} />
                        <AvatarFallback className="bg-blue-100 text-blue-600 text-xs">
                          {message.users.name.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                    )}
                    
                    <div className={`flex flex-col ${isOwn ? 'items-end' : 'items-start'}`}>
                      <div
                        className={`px-3 py-2 rounded-lg ${
                          isOwn
                            ? 'bg-blue-600 text-white'
                            : 'bg-slate-100 text-slate-900'
                        }`}
                      >
                        {!isOwn && (
                          <p className="text-xs font-medium mb-1 text-slate-600">
                            {message.users.name}
                          </p>
                        )}
                        
                        <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                        
                        {/* Attachments */}
                        {message.attachments && message.attachments.length > 0 && (
                          <div className="mt-2 space-y-2">
                            {message.attachments.map((attachment, index) =>
                              renderAttachment(attachment, index)
                            )}
                          </div>
                        )}
                      </div>
                      
                      <div className={`flex items-center space-x-1 mt-1 ${isOwn ? 'flex-row-reverse space-x-reverse' : ''}`}>
                        <span className="text-xs text-slate-500">
                          {formatMessageTime(message.sent_at)}
                        </span>
                        {isOwn && getStatusIcon(message.status)}
                      </div>
                    </div>
                  </div>
                </div>
              )
            })
          )}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>

      {/* Message Input */}
      <div className="border-t bg-slate-50">
        {/* Attachment Upload Section */}
        {showAttachments && (
          <div className="p-4 border-b bg-white">
            <MessageAttachmentUpload
              onFilesSelected={handleFilesSelected}
              onFileRemoved={handleFileRemoved}
              maxFiles={5}
              disabled={sending}
            />
          </div>
        )}

        {/* Message Input Row */}
        <div className="p-3 md:p-4">
          <div className="flex items-end space-x-2">
            <div className="flex-1">
              <Input
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Type a message..."
                disabled={sending}
                className="resize-none text-sm md:text-base"
              />
            </div>

            <Button
              size="sm"
              variant="outline"
              onClick={toggleAttachments}
              disabled={sending}
              className={showAttachments ? "bg-blue-50 border-blue-200" : ""}
            >
              <Paperclip className="h-4 w-4" />
            </Button>

            <Button
              size="sm"
              onClick={handleSendMessage}
              disabled={sending || (!newMessage.trim() && attachments.length === 0)}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {sending ? (
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>

          {/* Quick Attachment Preview */}
          {attachments.length > 0 && !showAttachments && (
            <div className="mt-2 flex items-center space-x-2">
              <span className="text-xs text-slate-500">
                {attachments.length} file(s) attached
              </span>
              <Button
                size="sm"
                variant="ghost"
                onClick={toggleAttachments}
                className="h-6 text-xs text-blue-600 hover:text-blue-700"
              >
                View
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => {
                  setAttachments([])
                  setShowAttachments(false)
                }}
                className="h-6 text-xs text-slate-500 hover:text-slate-700"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
